<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

try {
    // الحصول على الاتصال بقاعدة البيانات
    $pdo = getConnection();
    
    // استعلام لجلب جميع المنتجات النشطة
    $sql = "SELECT 
                id,
                name as roller_title,
                description as roller_about,
                price as watch_price,
                image as roller_src,
                category,
                'watches.html' as page_href,
                CONCAT(name, ' - ', description) as img_alt,
                name as header_title,
                description as header_subtitle,
                description as about_title,
                description as about_text,
                image as img_src,
                CONCAT(LOWER(REPLACE(name, ' ', '-')), '-style') as style_class,
                'sidepanel-roller' as sidepanel_class,
                name as footer_link_title,
                CONCAT(name, ' - ', description) as doc_title,
                '' as roller_srcset,
                '' as video_src,
                '' as video_srcset,
                '' as img_srcset
            FROM products 
            WHERE status = 'active' 
            ORDER BY created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    // تحويل البيانات إلى تنسيق JSON المطلوب للموقع
    $watches_data = [];
    foreach ($products as $product) {
        $watches_data[] = [
            'id' => strtolower(str_replace(' ', '_', $product['roller_title'])),
            'page_href' => $product['page_href'],
            'roller_srcset' => $product['roller_srcset'],
            'roller_src' => $product['roller_src'],
            'img_alt' => $product['img_alt'],
            'roller_title' => $product['roller_title'],
            'roller_about' => $product['roller_about'],
            'video_src' => $product['video_src'],
            'video_srcset' => $product['video_srcset'],
            'header_title' => $product['header_title'],
            'header_subtitle' => $product['header_subtitle'],
            'about_title' => $product['about_title'],
            'about_text' => $product['about_text'],
            'watch_price' => number_format($product['watch_price'], 0),
            'img_srcset' => $product['img_srcset'],
            'img_src' => $product['img_src'],
            'style_class' => $product['style_class'],
            'sidepanel_class' => $product['sidepanel_class'],
            'footer_link_title' => $product['footer_link_title'],
            'doc_title' => $product['doc_title']
        ];
    }
    
    // إرجاع البيانات بنفس تنسيق ملف JSON الأصلي
    $response = [
        'watches_data' => $watches_data
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // في حالة حدوث خطأ
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في قاعدة البيانات',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    // في حالة حدوث خطأ عام
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
