<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

try {
    // الحصول على الاتصال بقاعدة البيانات
    $pdo = getConnection();
    
    // استعلام لجلب جميع المنتجات النشطة
    $sql = "SELECT
                id,
                name,
                description,
                price,
                image,
                category,
                created_at,
                updated_at
            FROM products
            WHERE status = 'active'
            ORDER BY created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    // تحويل البيانات إلى تنسيق JSON المطلوب للموقع
    $watches_data = [];
    foreach ($products as $product) {
        // إنشاء معرف فريد من اسم المنتج
        $productId = strtolower(str_replace([' ', '-', '/', '\\'], '_', $product['name']));

        $watches_data[] = [
            'id' => $productId,
            'page_href' => 'watches.html',
            'roller_srcset' => '', // يمكن إضافة هذا لاحقاً
            'roller_src' => $product['image'],
            'img_alt' => $product['name'] . ' watch',
            'roller_title' => $product['name'],
            'roller_about' => substr($product['description'], 0, 100) . '...',
            'video_src' => '', // يمكن إضافة هذا لاحقاً
            'video_srcset' => '', // يمكن إضافة هذا لاحقاً
            'header_title' => $product['name'],
            'header_subtitle' => substr($product['description'], 0, 150) . '...',
            'about_title' => $product['name'],
            'about_text' => $product['description'],
            'watch_price' => number_format($product['price'], 0),
            'img_srcset' => '', // يمكن إضافة هذا لاحقاً
            'img_src' => $product['image'],
            'style_class' => $productId . '-style',
            'sidepanel_class' => 'sidepanel-roller',
            'footer_link_title' => $product['name'],
            'doc_title' => $product['name'] . ' - ' . substr($product['description'], 0, 100),
            'category' => $product['category'],
            'database_id' => $product['id'],
            'created_at' => $product['created_at'],
            'updated_at' => $product['updated_at']
        ];
    }
    
    // إرجاع البيانات بنفس تنسيق ملف JSON الأصلي
    $response = [
        'watches_data' => $watches_data
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // في حالة حدوث خطأ
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في قاعدة البيانات',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    // في حالة حدوث خطأ عام
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
