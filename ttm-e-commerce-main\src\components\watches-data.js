// API-based watches data loader
let watches_data = {};
let watchesArray = [];

/**
 * Fetch watches data from API
 * @returns {Promise<Object>} Watches data object
 */
async function fetchWatchesData() {
  try {
    const response = await fetch('/api/get_watches.php');

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.message || 'خطأ في جلب البيانات');
    }

    // Convert array to object format for compatibility
    const watchesObject = {};
    data.watches_data.forEach(watch => {
      watchesObject[watch.id] = watch;
    });

    watches_data = watchesObject;
    watchesArray = data.watches_data;

    return watches_data;

  } catch (error) {
    console.error('Error fetching watches data:', error);

    // Fallback to static data if API fails
    return getFallbackData();
  }
}

/**
 * Get fallback data when API is not available
 * @returns {Object} Fallback watches data
 */
function getFallbackData() {
  return {
    sea_dweller: {
      id: "sea_dweller",
      page_href: "watches.html",
      roller_srcset:
        "https://content.rolex.com/dam/new-watches-2020/homepage/roller/all-watches/watches_0005_m126603-0001-sea-dweller.jpg?imwidth=550,%20https://content.rolex.com/dam/new-watches-2020/homepage/roller/all-watches/watches_0005_m126603-0001-sea-dweller.jpg?imwidth=1080%202x",
      roller_src:
        "src/assets/media/img/img-watches-roller/watches-sea-dweller.webp",
      img_alt: "Rolex deepSea watch",
      roller_title: "sea-dweller",
      roller_about: "The watch that conquered depths",
      video_src:
        "src/assets/media/video/professional-watches-sea-dweller-cover-video.mp4",
      video_srcset:
        "https://content.rolex.com/dam/watches/family-pages/sea-dweller/video/cover/professional-watches-sea-dweller-cover-video.mp4",
      header_title: "sea - dweller",
      header_subtitle: "the watch that has conquered the depths",
      about_title:
        "The Sea-Dweller and Rolex Deepsea are ultra-resistant dive watches designed by Rolex for exploration of the seabed.",
      about_text:
        "Hermetic up to 1220 meters on the Rolex Sea Dweller models, released in 1967, and up to 3900 meters on the Deepsea models presented in 2008. They are the quintessential manifestation of Rolex's leadership in diving watches and the result of decades of collaboration with diving professionals.",
      watch_price: "10200",
      img_srcset:
        "https://content.rolex.com/dam/new-watches-2022/family-pages/sea-dweller/family-page-sea-dweller-deepsea-beauty_m136660_0003.jpg?imwidth=1350,%20https://content.rolex.com/dam/new-watches-2022/family-pages/sea-dweller/family-page-sea-dweller-deepsea-beauty_m136660_0003.jpg?imwidth=1668%202x",
      img_src:
        "src/assets/media/img/watch-pages-img/page-sea-dweller-deepsea-beauty.jpg",
      style_class: "sea-dweller-style",
      sidepanel_class: "sidepanel-roller",
      footer_link_title: "Sea-Dweller",
      doc_title: "Rolex Sea-Dweller - The watch that has conquered the depths",
    },
    air_king: {
      id: "air_king",
      page_href: "watches.html",
      roller_srcset:
        "https://content.rolex.com/dam/new-watches-2022/homepage/roller-family/homepage-new-watches-2022-roller-watches-air-king-family-m126900-0001.jpg?imwidth=550,%20https://content.rolex.com/dam/new-watches-2022/homepage/roller-family/homepage-new-watches-2022-roller-watches-air-king-family-m126900-0001.jpg?imwidth=1080%202x",
      roller_src: "src/assets/media/img/img-watches-roller/watches-air-king.webp",
      img_alt: "Rolex air-king watch",
      roller_title: "air-king",
      roller_about: "A tribute to aviation",
      video_src:
        "src/assets/media/video/professional-watches-new-air-king-2022-video.mp4",
      video_srcset:
        "https://content.rolex.com/dam/watches/family-pages/air-king/video/cover/professional-watches-new-air-king-2022-video.mp4",
      header_title: "air - king",
      header_subtitle: "a tribute to aviation",
      about_title:
        "the rolex air-king pays tribute to aviation pioneers and the role of the oyster in the air epic.",
      about_text:
        "The Air - King, fitted with a 40mm case, the Oyster solid-length Oystersteel bracelet and a distinctive black dial, punctuates the aeronautical heritage pf the original Rolex Oyster",
      watch_price: "9800",
      img_srcset:
        "https://content.rolex.com/dam/new-watches-2022/family-pages/air-king/family-page-air-king-beauty_m126900-0001_004.jpg?imwidth=1350,%20https://content.rolex.com/dam/new-watches-2022/family-pages/air-king/family-page-air-king-beauty_m126900-0001_004.jpg?imwidth=1668%202x",
      img_src: "src/assets/media/img/watch-pages-img/page-air-king-beauty.webp",
      style_class: "air-king-style",
      sidepanel_class: "sidepanel-roller",
      footer_link_title: "Air-King",
      doc_title: "Rolex Air-King - A tribute to aviation",
    }
  };
}

/**
 * Initialize watches data on page load
 * @returns {Promise<void>}
 */
async function initializeWatchesData() {
  try {
    await fetchWatchesData();
    console.log('Watches data loaded successfully');
  } catch (error) {
    console.error('Failed to initialize watches data:', error);
  }
}

/**
 * Get watches data (async)
 * @returns {Promise<Object>} Watches data object
 */
async function getWatchesData() {
  if (Object.keys(watches_data).length === 0) {
    await fetchWatchesData();
  }
  return watches_data;
}

/**
 * Get watches array (async)
 * @returns {Promise<Array>} Watches data array
 */
async function getWatchesArray() {
  if (watchesArray.length === 0) {
    await fetchWatchesData();
  }
  return watchesArray;
}

/**
 * Get specific watch by ID
 * @param {string} watchId - Watch ID
 * @returns {Promise<Object|null>} Watch data or null if not found
 */
async function getWatchById(watchId) {
  const data = await getWatchesData();
  return data[watchId] || null;
}

/**
 * Search watches by title or description
 * @param {string} searchTerm - Search term
 * @returns {Promise<Array>} Array of matching watches
 */
async function searchWatches(searchTerm) {
  const array = await getWatchesArray();
  const term = searchTerm.toLowerCase();

  return array.filter(watch =>
    watch.roller_title.toLowerCase().includes(term) ||
    watch.roller_about.toLowerCase().includes(term) ||
    watch.about_title.toLowerCase().includes(term)
  );
}

// Export functions and data
export {
  watches_data,
  fetchWatchesData,
  initializeWatchesData,
  getWatchesData,
  getWatchesArray,
  getWatchById,
  searchWatches
};

// Auto-initialize when module loads
initializeWatchesData();
