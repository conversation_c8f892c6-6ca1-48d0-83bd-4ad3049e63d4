<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات إدارة قاعدة البيانات - TTM E-Commerce</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .tool-icon {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }
        
        .tool-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        
        .tool-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        
        .tool-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .tool-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .tool-btn.secondary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .tool-btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .tool-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }
        
        .status-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        
        .status-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .status-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #007cba;
        }
        
        .status-number {
            font-size: 2em;
            font-weight: bold;
            color: #007cba;
            margin-bottom: 5px;
        }
        
        .status-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
        
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .alert.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .alert.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ أدوات إدارة قاعدة البيانات</h1>
            <p>TTM E-Commerce Database Management Tools</p>
        </div>

        <?php
        // محاولة الاتصال بقاعدة البيانات للحصول على الحالة
        $dbStatus = false;
        $stats = [];
        
        try {
            require_once 'config/database.php';
            $pdo = getConnection();
            $dbStatus = true;
            
            // جلب الإحصائيات
            $stats = [
                'products' => $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'")->fetchColumn(),
                'orders' => $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn(),
                'admins' => $pdo->query("SELECT COUNT(*) FROM admins WHERE status = 'active'")->fetchColumn(),
                'total_sales' => $pdo->query("SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE status IN ('delivered', 'processing')")->fetchColumn()
            ];
        } catch (Exception $e) {
            $dbStatus = false;
            $dbError = $e->getMessage();
        }
        ?>

        <!-- حالة قاعدة البيانات -->
        <?php if ($dbStatus): ?>
            <div class="alert success">
                ✅ قاعدة البيانات متصلة وتعمل بشكل طبيعي
            </div>
            
            <div class="status-section">
                <h2 class="status-title">📊 إحصائيات سريعة</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-number"><?php echo $stats['products']; ?></div>
                        <div class="status-label">المنتجات النشطة</div>
                    </div>
                    <div class="status-item">
                        <div class="status-number"><?php echo $stats['orders']; ?></div>
                        <div class="status-label">إجمالي الطلبات</div>
                    </div>
                    <div class="status-item">
                        <div class="status-number"><?php echo $stats['admins']; ?></div>
                        <div class="status-label">المديرين النشطين</div>
                    </div>
                    <div class="status-item">
                        <div class="status-number"><?php echo number_format($stats['total_sales'], 0); ?></div>
                        <div class="status-label">إجمالي المبيعات (جنيه)</div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="alert error">
                ❌ خطأ في الاتصال بقاعدة البيانات: <?php echo isset($dbError) ? htmlspecialchars($dbError) : 'خطأ غير معروف'; ?>
            </div>
        <?php endif; ?>

        <!-- شبكة الأدوات -->
        <div class="tools-grid">
            <!-- اختبار الاتصال -->
            <div class="tool-card">
                <span class="tool-icon">🔍</span>
                <h3 class="tool-title">اختبار الاتصال</h3>
                <p class="tool-description">
                    فحص شامل لحالة قاعدة البيانات والتأكد من وجود جميع الجداول المطلوبة
                </p>
                <a href="test_connection.php" class="tool-btn">تشغيل الاختبار</a>
            </div>

            <!-- إدراج البيانات الأساسية -->
            <div class="tool-card">
                <span class="tool-icon">📥</span>
                <h3 class="tool-title">إدراج البيانات الأساسية</h3>
                <p class="tool-description">
                    نقل بيانات الساعات والمستخدمين من ملفات JSON إلى قاعدة البيانات
                </p>
                <a href="import_data.php" class="tool-btn secondary">إدراج البيانات</a>
            </div>

            <!-- إضافة بيانات تجريبية -->
            <div class="tool-card">
                <span class="tool-icon">🎯</span>
                <h3 class="tool-title">إضافة بيانات تجريبية</h3>
                <p class="tool-description">
                    إضافة المزيد من الساعات والطلبات التجريبية لاختبار النظام
                </p>
                <a href="add_sample_data.php" class="tool-btn warning">إضافة البيانات</a>
            </div>

            <!-- عرض محتويات قاعدة البيانات -->
            <div class="tool-card">
                <span class="tool-icon">📊</span>
                <h3 class="tool-title">عرض محتويات قاعدة البيانات</h3>
                <p class="tool-description">
                    استعراض جميع البيانات الموجودة في المنتجات والطلبات والمديرين
                </p>
                <a href="view_database.php" class="tool-btn">عرض البيانات</a>
            </div>

            <!-- الموقع الرئيسي -->
            <div class="tool-card">
                <span class="tool-icon">🏠</span>
                <h3 class="tool-title">الموقع الرئيسي</h3>
                <p class="tool-description">
                    العودة إلى الموقع الرئيسي لمتجر الساعات
                </p>
                <a href="index.html" class="tool-btn">زيارة الموقع</a>
            </div>

            <!-- لوحة الإدارة -->
            <div class="tool-card">
                <span class="tool-icon">⚙️</span>
                <h3 class="tool-title">لوحة الإدارة</h3>
                <p class="tool-description">
                    الوصول إلى لوحة إدارة المنتجات والطلبات
                </p>
                <a href="admin.html" class="tool-btn">لوحة الإدارة</a>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="status-section">
            <h2 class="status-title">📚 معلومات مهمة</h2>
            <div style="text-align: right; line-height: 1.8;">
                <p><strong>🔧 إعداد قاعدة البيانات:</strong> تأكد من تحديث ملف <code>config/database.php</code> بإعدادات قاعدة البيانات الصحيحة</p>
                <p><strong>📁 ملفات API:</strong> جميع ملفات API موجودة في مجلد <code>api/</code> وجاهزة للاستخدام</p>
                <p><strong>🔐 الأمان:</strong> كلمات المرور مشفرة وجميع الاستعلامات محمية من SQL Injection</p>
                <p><strong>📖 الدليل:</strong> راجع ملف <code>DATABASE_INTEGRATION_README.md</code> للحصول على دليل شامل</p>
            </div>
        </div>

        <!-- API Endpoints -->
        <div class="status-section">
            <h2 class="status-title">🔗 نقاط API المتاحة</h2>
            <div style="text-align: right; line-height: 1.8;">
                <p><strong>GET</strong> <code>/api/get_watches.php</code> - جلب بيانات الساعات</p>
                <p><strong>GET</strong> <code>/api/get_users.php</code> - جلب بيانات المستخدمين</p>
                <p><strong>POST</strong> <code>/api/login.php</code> - تسجيل الدخول</p>
                <p><strong>GET/POST/PUT/DELETE</strong> <code>/api/manage_products.php</code> - إدارة المنتجات</p>
                <p><strong>GET/POST/PUT/DELETE</strong> <code>/api/manage_orders.php</code> - إدارة الطلبات</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 TTM E-Commerce - نظام إدارة قاعدة البيانات</p>
            <p>تم تطوير النظام بنجاح وربطه بقاعدة البيانات</p>
        </div>
    </div>
</body>
</html>
