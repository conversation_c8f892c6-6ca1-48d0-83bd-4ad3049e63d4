// API-based login validation
const loginForm = document.querySelector(".modal-content");
const inputUsername = document.querySelector("[data-username]");
const inputUserPassword = document.querySelector("[data-user-password]");

// Add loading indicator
const createLoadingIndicator = () => {
  const loading = document.createElement('div');
  loading.className = 'login-loading';
  loading.innerHTML = '<span>جاري تسجيل الدخول...</span>';
  loading.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 1000;
  `;
  return loading;
};

loginForm.addEventListener("submit", async (event) => {
  event.preventDefault();
  await dataToValidate();
});

const dataToValidate = async () => {
  const inputUn = inputUsername.value.trim();
  const inputPw = inputUserPassword.value.trim();

  // Validate input
  if (!inputUn || !inputPw) {
    showError('يرجى إدخال اسم المستخدم وكلمة المرور');
    styleValidation(false);
    return;
  }

  // Show loading
  const loading = createLoadingIndicator();
  loginForm.appendChild(loading);

  try {
    // Call login API
    const response = await fetch('/api/login.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: inputUn,
        password: inputPw
      })
    });

    const result = await response.json();

    if (result.success) {
      console.log("Access granted");

      // Store user data in localStorage
      localStorage.setItem('user_data', JSON.stringify(result.user));
      localStorage.setItem('logged_in', 'true');

      // Redirect to admin page
      window.location.href = "admin.html";
      styleValidation(true);
    } else {
      showError(result.message || 'خطأ في تسجيل الدخول');
      styleValidation(false);
    }

  } catch (error) {
    console.error('Login error:', error);
    showError('خطأ في الاتصال بالخادم');
    styleValidation(false);
  } finally {
    // Remove loading indicator
    if (loading.parentNode) {
      loading.parentNode.removeChild(loading);
    }
  }
};

const showError = (message) => {
  // Remove existing error messages
  const existingError = document.querySelector('.login-error');
  if (existingError) {
    existingError.remove();
  }

  // Create error message
  const errorDiv = document.createElement('div');
  errorDiv.className = 'login-error';
  errorDiv.textContent = message;
  errorDiv.style.cssText = `
    color: #ff4444;
    background: #ffebee;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
    text-align: center;
    border: 1px solid #ff4444;
  `;

  // Insert error message before form buttons
  const formButtons = loginForm.querySelector('.modal-content__buttons');
  if (formButtons) {
    loginForm.insertBefore(errorDiv, formButtons);
  } else {
    loginForm.appendChild(errorDiv);
  }

  // Auto-remove error after 5 seconds
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 5000);
};

const styleValidation = (boolean) => {
  if (boolean) {
    inputUsername.value = "";
    inputUserPassword.value = "";
    // Remove error styling
    inputUsername.classList.remove("input--invalid");
    inputUserPassword.classList.remove("input--invalid");
  } else {
    // Add error styling
    inputUsername.classList.add("input--invalid");
    inputUserPassword.classList.add("input--invalid");
  }
};

// Check if user is already logged in
const checkLoginStatus = () => {
  const isLoggedIn = localStorage.getItem('logged_in');
  const userData = localStorage.getItem('user_data');

  if (isLoggedIn === 'true' && userData) {
    console.log('User already logged in:', JSON.parse(userData));
  }
};

// Initialize login status check
checkLoginStatus();
