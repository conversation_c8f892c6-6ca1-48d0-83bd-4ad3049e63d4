<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

// التحقق من طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

try {
    // الحصول على الاتصال بقاعدة البيانات
    $pdo = getConnection();
    
    switch ($method) {
        case 'GET':
            handleGetOrders($pdo);
            break;
            
        case 'POST':
            handleCreateOrder($pdo);
            break;
            
        case 'PUT':
            handleUpdateOrder($pdo);
            break;
            
        case 'DELETE':
            handleDeleteOrder($pdo);
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'طريقة الطلب غير مسموحة'
            ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * جلب جميع الطلبات
 */
function handleGetOrders($pdo) {
    try {
        $sql = "SELECT 
                    o.*,
                    GROUP_CONCAT(
                        CONCAT(oi.product_name, ' (', oi.quantity, 'x', oi.product_price, ')')
                        SEPARATOR ', '
                    ) as order_items
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                GROUP BY o.id
                ORDER BY o.created_at DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $orders = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $orders
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب الطلبات: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * إنشاء طلب جديد
 */
function handleCreateOrder($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($input['customer_name']) || !isset($input['customer_phone']) || 
        !isset($input['customer_address']) || !isset($input['items'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'بيانات العميل والمنتجات مطلوبة'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        // بدء المعاملة
        $pdo->beginTransaction();
        
        // حساب المجموع الكلي
        $totalAmount = 0;
        foreach ($input['items'] as $item) {
            $totalAmount += $item['price'] * $item['quantity'];
        }
        
        // إدراج الطلب
        $orderSql = "INSERT INTO orders (customer_name, customer_phone, customer_address, total_amount, notes) 
                     VALUES (?, ?, ?, ?, ?)";
        
        $orderStmt = $pdo->prepare($orderSql);
        $orderResult = $orderStmt->execute([
            $input['customer_name'],
            $input['customer_phone'],
            $input['customer_address'],
            $totalAmount,
            $input['notes'] ?? ''
        ]);
        
        if (!$orderResult) {
            throw new Exception('فشل في إنشاء الطلب');
        }
        
        $orderId = $pdo->lastInsertId();
        
        // إدراج عناصر الطلب
        $itemSql = "INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, subtotal) 
                    VALUES (?, ?, ?, ?, ?, ?)";
        
        $itemStmt = $pdo->prepare($itemSql);
        
        foreach ($input['items'] as $item) {
            $subtotal = $item['price'] * $item['quantity'];
            $itemResult = $itemStmt->execute([
                $orderId,
                $item['product_id'],
                $item['name'],
                $item['price'],
                $item['quantity'],
                $subtotal
            ]);
            
            if (!$itemResult) {
                throw new Exception('فشل في إضافة عنصر الطلب');
            }
        }
        
        // تأكيد المعاملة
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إنشاء الطلب بنجاح',
            'order_id' => $orderId,
            'total_amount' => $totalAmount
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        // إلغاء المعاملة في حالة الخطأ
        $pdo->rollBack();
        
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في إنشاء الطلب: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * تحديث حالة الطلب
 */
function handleUpdateOrder($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف الطلب مطلوب'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        // التحقق من وجود الطلب
        $checkSql = "SELECT id FROM orders WHERE id = ?";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([$input['id']]);
        
        if (!$checkStmt->fetch()) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'الطلب غير موجود'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        
        // تحديث الطلب
        $sql = "UPDATE orders SET 
                status = COALESCE(?, status),
                notes = COALESCE(?, notes),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $input['status'] ?? null,
            $input['notes'] ?? null,
            $input['id']
        ]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث الطلب بنجاح'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception('فشل في تحديث الطلب');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في تحديث الطلب: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * حذف طلب
 */
function handleDeleteOrder($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف الطلب مطلوب'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        // بدء المعاملة
        $pdo->beginTransaction();
        
        // حذف عناصر الطلب أولاً
        $deleteItemsSql = "DELETE FROM order_items WHERE order_id = ?";
        $deleteItemsStmt = $pdo->prepare($deleteItemsSql);
        $deleteItemsStmt->execute([$input['id']]);
        
        // حذف الطلب
        $deleteOrderSql = "DELETE FROM orders WHERE id = ?";
        $deleteOrderStmt = $pdo->prepare($deleteOrderSql);
        $result = $deleteOrderStmt->execute([$input['id']]);
        
        if ($result && $deleteOrderStmt->rowCount() > 0) {
            // تأكيد المعاملة
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف الطلب بنجاح'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            // إلغاء المعاملة
            $pdo->rollBack();
            
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'الطلب غير موجود'
            ], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (PDOException $e) {
        // إلغاء المعاملة في حالة الخطأ
        $pdo->rollBack();
        
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في حذف الطلب: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}
?>
