.input--holder {
  padding: 2rem;
  height: 50vh;
  overflow: auto;
}

.input--holder::-webkit-scrollbar {
  width: 0.5rem;
}

.input--holder::-webkit-scrollbar-track {
  background: var(--style-milgauss-gray);
}

.input--holder::-webkit-scrollbar-thumb {
  background: var(--style-color-gold);
  border-radius: 1rem;
}

.data-input {
  width: 100%;
  padding: 0.5rem 1.1rem;
  margin: 8px 0;
  display: inline-block;
  border: 2px solid var(--style-color-gold);
  border-radius: 0.5rem;
  box-sizing: border-box;
}

.label-title {
  font-family: var(--font-family-light);
  font-size: 0.8rem;
}

.admin-items.modal {
  display: none;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-top: 4rem;
}

.admin-items.modal--show {
  display: block;
}

.modal-content {
  background-color: #fefefe;
  margin: 5% auto 15% auto;
  border: 3px solid var(--style-color-green);
  border-radius: 0.4rem;
  width: 80%;
}

.animate {
  -webkit-animation: animatezoom 0.6s;
  animation: animatezoom 0.6s;
}

.btn--holder {
  display: flex;
  justify-content: space-around;
  border-top: 2px solid var(--style-color-gold);
  padding: 1rem;
}

button.save-item,
button.cancel-edit,
button.cancel-new {
  font-family: var(--font-family-regular);
  font-size: 0.8rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

button.save-item {
  background-color: var(--style-color-green);
  color: var(--style-color-white);
  min-width: 5rem;
  width: 30%;
}

button.cancel-edit,
button.cancel-new {
  background-color: var(--style-color-white);
  border: 2px solid var(--style-gmt-master-red);
  color: var(--style-gmt-master-red);
}

button.save-item:hover {
  background-color: var(--style-color-lightGreen);
}

button.cancel-edit:hover,
button.cancel-new:hover {
  background-color: var(--style-gmt-master-red);
  color: var(--style-color-white);
}
