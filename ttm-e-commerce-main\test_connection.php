<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * استخدم هذا الملف للتأكد من أن الاتصال بقاعدة البيانات يعمل بشكل صحيح
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بقاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007cba;
            background-color: #f8f9fa;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            border-left-color: #17a2b8;
            background-color: #d1ecf1;
            color: #0c5460;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success {
            background-color: #28a745;
            color: white;
        }
        .status-error {
            background-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار الاتصال بقاعدة البيانات</h1>
            <p>TTM E-Commerce Database Connection Test</p>
        </div>

        <?php
        $tests = [];
        $overallStatus = true;

        // اختبار 1: الاتصال بقاعدة البيانات
        try {
            $pdo = getConnection();
            $tests[] = [
                'name' => 'الاتصال بقاعدة البيانات',
                'status' => 'success',
                'message' => 'تم الاتصال بقاعدة البيانات بنجاح'
            ];
        } catch (Exception $e) {
            $tests[] = [
                'name' => 'الاتصال بقاعدة البيانات',
                'status' => 'error',
                'message' => 'فشل الاتصال: ' . $e->getMessage()
            ];
            $overallStatus = false;
        }

        // اختبار 2: فحص الجداول المطلوبة
        if (isset($pdo)) {
            $requiredTables = ['products', 'orders', 'order_items', 'admins'];
            $existingTables = [];
            
            try {
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                foreach ($requiredTables as $table) {
                    if (in_array($table, $tables)) {
                        $existingTables[] = $table;
                    }
                }
                
                if (count($existingTables) === count($requiredTables)) {
                    $tests[] = [
                        'name' => 'فحص الجداول المطلوبة',
                        'status' => 'success',
                        'message' => 'جميع الجداول المطلوبة موجودة: ' . implode(', ', $existingTables)
                    ];
                } else {
                    $missingTables = array_diff($requiredTables, $existingTables);
                    $tests[] = [
                        'name' => 'فحص الجداول المطلوبة',
                        'status' => 'error',
                        'message' => 'الجداول المفقودة: ' . implode(', ', $missingTables)
                    ];
                    $overallStatus = false;
                }
            } catch (Exception $e) {
                $tests[] = [
                    'name' => 'فحص الجداول المطلوبة',
                    'status' => 'error',
                    'message' => 'خطأ في فحص الجداول: ' . $e->getMessage()
                ];
                $overallStatus = false;
            }
        }

        // اختبار 3: فحص بيانات المنتجات
        if (isset($pdo)) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
                $result = $stmt->fetch();
                $productCount = $result['count'];
                
                $tests[] = [
                    'name' => 'فحص بيانات المنتجات',
                    'status' => 'success',
                    'message' => "عدد المنتجات النشطة: {$productCount}"
                ];
            } catch (Exception $e) {
                $tests[] = [
                    'name' => 'فحص بيانات المنتجات',
                    'status' => 'error',
                    'message' => 'خطأ في جلب بيانات المنتجات: ' . $e->getMessage()
                ];
                $overallStatus = false;
            }
        }

        // اختبار 4: فحص بيانات المديرين
        if (isset($pdo)) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins WHERE status = 'active'");
                $result = $stmt->fetch();
                $adminCount = $result['count'];
                
                $tests[] = [
                    'name' => 'فحص بيانات المديرين',
                    'status' => 'success',
                    'message' => "عدد المديرين النشطين: {$adminCount}"
                ];
            } catch (Exception $e) {
                $tests[] = [
                    'name' => 'فحص بيانات المديرين',
                    'status' => 'error',
                    'message' => 'خطأ في جلب بيانات المديرين: ' . $e->getMessage()
                ];
                $overallStatus = false;
            }
        }

        // اختبار 5: فحص إعدادات PHP
        $phpTests = [
            'PDO MySQL' => extension_loaded('pdo_mysql'),
            'JSON' => extension_loaded('json'),
            'cURL' => extension_loaded('curl')
        ];

        $phpStatus = true;
        $phpMessage = '';
        foreach ($phpTests as $ext => $loaded) {
            if (!$loaded) {
                $phpStatus = false;
                $phpMessage .= "مفقود: {$ext} ";
            }
        }

        if ($phpStatus) {
            $tests[] = [
                'name' => 'فحص إعدادات PHP',
                'status' => 'success',
                'message' => 'جميع الإضافات المطلوبة متوفرة'
            ];
        } else {
            $tests[] = [
                'name' => 'فحص إعدادات PHP',
                'status' => 'error',
                'message' => $phpMessage
            ];
            $overallStatus = false;
        }
        ?>

        <!-- عرض النتائج -->
        <div class="test-section <?php echo $overallStatus ? 'success' : 'error'; ?>">
            <h2>📊 النتيجة العامة</h2>
            <p>
                <strong>الحالة:</strong> 
                <span class="status-badge <?php echo $overallStatus ? 'status-success' : 'status-error'; ?>">
                    <?php echo $overallStatus ? 'نجح' : 'فشل'; ?>
                </span>
            </p>
            <?php if ($overallStatus): ?>
                <p>✅ جميع الاختبارات نجحت! قاعدة البيانات جاهزة للاستخدام.</p>
            <?php else: ?>
                <p>❌ بعض الاختبارات فشلت. يرجى مراجعة التفاصيل أدناه.</p>
            <?php endif; ?>
        </div>

        <div class="test-section info">
            <h2>📋 تفاصيل الاختبارات</h2>
            <table>
                <thead>
                    <tr>
                        <th>الاختبار</th>
                        <th>الحالة</th>
                        <th>التفاصيل</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($tests as $test): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($test['name']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $test['status'] === 'success' ? 'status-success' : 'status-error'; ?>">
                                    <?php echo $test['status'] === 'success' ? 'نجح' : 'فشل'; ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($test['message']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="test-section info">
            <h2>ℹ️ معلومات النظام</h2>
            <table>
                <tr>
                    <th>إصدار PHP</th>
                    <td><?php echo PHP_VERSION; ?></td>
                </tr>
                <tr>
                    <th>نظام التشغيل</th>
                    <td><?php echo PHP_OS; ?></td>
                </tr>
                <tr>
                    <th>الخادم</th>
                    <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></td>
                </tr>
                <tr>
                    <th>التوقيت</th>
                    <td><?php echo date('Y-m-d H:i:s'); ?></td>
                </tr>
            </table>
        </div>

        <?php if (!$overallStatus): ?>
        <div class="test-section error">
            <h2>🔧 خطوات الإصلاح</h2>
            <ol>
                <li>تأكد من إعدادات قاعدة البيانات في ملف <code>config/database.php</code></li>
                <li>تأكد من تشغيل خادم MySQL</li>
                <li>تأكد من وجود قاعدة البيانات والجداول المطلوبة</li>
                <li>تأكد من صلاحيات المستخدم للوصول إلى قاعدة البيانات</li>
                <li>تأكد من تثبيت الإضافات المطلوبة لـ PHP</li>
            </ol>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
