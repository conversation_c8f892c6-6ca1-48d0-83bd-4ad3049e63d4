.roller.roller-on-sidepanel {
  margin-bottom: 2rem;
}

.sidepanel-watches__roller-container {
  margin-top: 2rem;
  margin-left: 1.5rem;
}

.roller__item.sidepanel-roller {
  overflow: hidden;
  text-decoration: none;
  border-top-left-radius: unset;
  border-top-right-radius: unset;
  height: 100%;
  width: 50%;
}

.item__title.sidepanel-roller,
.item__about.sidepanel-roller {
  background-color: var(--style-color-darkBrown);
  color: var(--style-color-white);
  padding-right: 0.5rem;
  font-size: 0.7rem;
}

.item__title.sidepanel-roller {
  letter-spacing: 0.1rem;
}

.roller__img.sidepanel-roller {
  border-radius: unset;
  transform-origin: center;
  transition: all 0.5s ease;
  width: 100%;
}

.roller.roller-on-sidepanel::-webkit-scrollbar {
  height: 0.3rem;
}

.roller.roller-on-sidepanel::-webkit-scrollbar-track {
  background: var(--style-color-gray);
}

.roller.roller-on-sidepanel::-webkit-scrollbar-thumb {
  background: var(--style-color-white);
  border-radius: 1rem;
}

/* .sidepanel-roller__btn--holder {
  display: none;
} */
/* .sidepanel-roller__dots {
  margin: 0.5rem 0 1rem 0;
} */
