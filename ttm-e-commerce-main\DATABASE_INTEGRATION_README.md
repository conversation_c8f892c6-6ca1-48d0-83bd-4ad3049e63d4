# دليل ربط قاعدة البيانات بموقع TTM E-Commerce

## نظرة عامة
تم ربط موقع TTM E-Commerce بقاعدة البيانات MySQL بنجاح. الآن يمكن للموقع جلب البيانات من قاعدة البيانات بدلاً من ملفات JSON الثابتة.

## الملفات المضافة/المحدثة

### 1. ملفات قاعدة البيانات
- `database.sql` - هيكل قاعدة البيانات الموجودة
- `config/database.php` - إعدادات الاتصال بقاعدة البيانات

### 2. ملفات API
- `api/get_watches.php` - جلب بيانات الساعات من قاعدة البيانات
- `api/get_users.php` - جلب بيانات المستخدمين
- `api/login.php` - تسجيل الدخول عبر قاعدة البيانات
- `api/manage_products.php` - إدارة المنتجات (إضافة، تعديل، حذف)
- `api/manage_orders.php` - إدارة الطلبات

### 3. ملفات JavaScript المحدثة
- `src/components/watches-data.js` - محدث ليستخدم API بدلاً من البيانات الثابتة
- `src/admin-controller/validate-login.js` - محدث ليستخدم API تسجيل الدخول

## إعداد قاعدة البيانات

### 1. إعدادات الاتصال
قم بتحديث ملف `config/database.php` بإعدادات قاعدة البيانات الخاصة بك:

```php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'your_database_name');
```

### 2. إنشاء قاعدة البيانات
قم بتشغيل ملف `database.sql` في phpMyAdmin أو MySQL command line:

```sql
mysql -u username -p database_name < database.sql
```

## استخدام النظام الجديد

### 1. جلب بيانات الساعات
```javascript
// استخدام الدالة الجديدة
const watchesData = await getWatchesData();
const watchesArray = await getWatchesArray();

// البحث في الساعات
const searchResults = await searchWatches('rolex');

// جلب ساعة محددة
const watch = await getWatchById('sea_dweller');
```

### 2. تسجيل الدخول
النظام الآن يستخدم API لتسجيل الدخول:
- يتم التحقق من البيانات في قاعدة البيانات
- يتم تشفير كلمات المرور
- يتم حفظ بيانات المستخدم في localStorage

### 3. إدارة المنتجات
```javascript
// جلب جميع المنتجات
fetch('/api/manage_products.php')

// إضافة منتج جديد
fetch('/api/manage_products.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        name: 'اسم المنتج',
        description: 'وصف المنتج',
        price: 1000,
        image: 'رابط الصورة'
    })
})

// تحديث منتج
fetch('/api/manage_products.php', {
    method: 'PUT',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        id: 1,
        name: 'اسم جديد',
        price: 1500
    })
})

// حذف منتج
fetch('/api/manage_products.php', {
    method: 'DELETE',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({id: 1})
})
```

### 4. إدارة الطلبات
```javascript
// جلب جميع الطلبات
fetch('/api/manage_orders.php')

// إنشاء طلب جديد
fetch('/api/manage_orders.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        customer_name: 'اسم العميل',
        customer_phone: '123456789',
        customer_address: 'عنوان العميل',
        items: [
            {
                product_id: 1,
                name: 'ساعة رولكس',
                price: 1000,
                quantity: 1
            }
        ]
    })
})
```

## المميزات الجديدة

### 1. نظام المصادقة المحسن
- تشفير كلمات المرور
- جلسات آمنة
- رسائل خطأ واضحة

### 2. إدارة البيانات الديناميكية
- إضافة منتجات جديدة من لوحة الإدارة
- تعديل المنتجات الموجودة
- حذف المنتجات (حذف منطقي)

### 3. نظام الطلبات
- إنشاء طلبات جديدة
- تتبع حالة الطلبات
- إدارة عناصر الطلبات

### 4. التوافق مع النظام القديم
- النظام يدعم البيانات الاحتياطية في حالة عدم توفر API
- واجهة المستخدم تبقى كما هي

## متطلبات الخادم

### 1. متطلبات PHP
- PHP 7.4 أو أحدث
- PDO MySQL extension
- JSON extension

### 2. متطلبات قاعدة البيانات
- MySQL 5.7 أو أحدث
- أو MariaDB 10.2 أو أحدث

### 3. إعدادات الخادم
- تفعيل mod_rewrite (للـ URL الودود)
- تفعيل CORS headers

## الأمان

### 1. حماية قاعدة البيانات
- استخدام Prepared Statements
- تشفير كلمات المرور
- التحقق من صحة البيانات

### 2. حماية API
- التحقق من طرق الطلب
- معالجة الأخطاء بشكل آمن
- تنظيف البيانات المدخلة

## استكشاف الأخطاء

### 1. مشاكل الاتصال بقاعدة البيانات
- تحقق من إعدادات `config/database.php`
- تأكد من تشغيل خادم MySQL
- تحقق من صلاحيات المستخدم

### 2. مشاكل API
- تحقق من error logs الخادم
- تأكد من تفعيل PHP extensions المطلوبة
- تحقق من CORS headers

### 3. مشاكل JavaScript
- افتح Developer Console في المتصفح
- تحقق من Network tab للطلبات الفاشلة
- تأكد من تحميل ملفات JavaScript بشكل صحيح

## الدعم والصيانة

### 1. النسخ الاحتياطي
- قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
- احتفظ بنسخة من ملفات الموقع

### 2. التحديثات
- راقب logs الخادم للأخطاء
- قم بتحديث PHP وMySQL بانتظام
- اختبر النظام بعد أي تحديثات

### 3. المراقبة
- راقب أداء قاعدة البيانات
- تحقق من مساحة التخزين
- راقب حركة المرور على API
