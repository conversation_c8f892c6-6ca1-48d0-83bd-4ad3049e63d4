<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

try {
    // الحصول على الاتصال بقاعدة البيانات
    $pdo = getConnection();
    
    // استعلام لجلب المديرين النشطين
    $sql = "SELECT 
                id,
                username,
                email,
                full_name,
                role,
                status,
                last_login,
                created_at
            FROM admins 
            WHERE status = 'active' 
            ORDER BY created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    // تحويل البيانات إلى تنسيق JSON المطلوب للموقع (متوافق مع ملف login-users.json)
    $users_data = [];
    foreach ($admins as $admin) {
        $users_data[] = [
            'id' => $admin['id'],
            'username' => $admin['username'],
            'password' => '******', // لا نعرض كلمة المرور الحقيقية
            'email' => $admin['email'],
            'full_name' => $admin['full_name'],
            'role' => $admin['role'],
            'status' => $admin['status'],
            'last_login' => $admin['last_login'],
            'created_at' => $admin['created_at']
        ];
    }
    
    // إرجاع البيانات بنفس تنسيق ملف JSON الأصلي
    $response = [
        'users' => $users_data
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    // في حالة حدوث خطأ
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في قاعدة البيانات',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    // في حالة حدوث خطأ عام
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
