.user-input {
  width: 100%;
  padding: 0.8rem 1.2rem;
  margin: 0.5rem 0;
  display: inline-block;
  border: 1px solid var(--style-color-gold);
  box-sizing: border-box;
}

.input--invalid {
  border: 1px solid red;
}

.user-input:-webkit-autofill,
.user-input:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--style-color-darkBrown);
  box-shadow: 0 0 0 1000px var(--style-color-white) inset;
}

.label-title,
.submit-btn {
  font-size: 0.8rem;
  letter-spacing: 1.5px;
  font-family: var(--font-family-light), Helvetica;
  color: var(--style-color-darkBrown);
}

.submit-btn {
  background-color: var(--style-color-gold);
  color: var(--style-color-white);
  padding: 1rem 1.1rem;
  margin: 0.5rem 0;
  border: none;
  width: 100%;
  opacity: 0.8;
  transition: all 0.2s ease-in-out;
}

.submit-btn:hover {
  opacity: 1;
}

.logo-holder {
  text-align: center;
  margin: 24px 0 12px 0;
  position: relative;
}

img.logo {
  width: 20%;
}

.container {
  padding: 16px;
}

.modal {
  display: none;
  position: fixed;
  z-index: 30;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-top: 2.5rem;
}

.modal--show {
  display: block;
}

.modal-content {
  background-color: rgba(255, 255, 255, 0.935);
  margin: 5% auto 15% auto;
  border: 1px solid var(--style-color-white);
  width: 80%;
  border-radius: 0.2rem;
}

.close-modal {
  position: absolute;
  left: 25px;
  top: 0;
  color: var(--style-color-gold);
  font-size: 1.7rem;
  transition: all 0.2s ease-in-out;
}

.close-modal:hover,
.close-modal:focus {
  color: var(--style-color-green);
}

.animate {
  -webkit-animation: animatezoom 0.6s;
  animation: animatezoom 0.6s;
}
