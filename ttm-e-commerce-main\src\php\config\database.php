<?php
/**
 * Database Configuration for TTM E-Commerce
 * This file contains database connection settings and PDO connection class
 */

class Database {
    // Database configuration
    private $host = 'localhost';
    private $db_name = 'ttm_ecommerce';
    private $username = 'root'; // Change this to your database username
    private $password = '';     // Change this to your database password
    private $charset = 'utf8mb4';
    
    private $pdo;
    private $error;

    /**
     * Create database connection
     * @return PDO|null
     */
    public function connect() {
        $this->pdo = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            echo "Connection Error: " . $this->error;
        }
        
        return $this->pdo;
    }
    
    /**
     * Get the last error message
     * @return string
     */
    public function getError() {
        return $this->error;
    }
}

/**
 * Get database connection instance
 * @return PDO|null
 */
function getDBConnection() {
    $database = new Database();
    return $database->connect();
}

/**
 * Execute a prepared statement safely
 * @param PDO $pdo
 * @param string $sql
 * @param array $params
 * @return PDOStatement|false
 */
function executeQuery($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Database Query Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Fetch all records from a query
 * @param PDO $pdo
 * @param string $sql
 * @param array $params
 * @return array|false
 */
function fetchAll($pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

/**
 * Fetch single record from a query
 * @param PDO $pdo
 * @param string $sql
 * @param array $params
 * @return array|false
 */
function fetchOne($pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

/**
 * Insert data and return last insert ID
 * @param PDO $pdo
 * @param string $sql
 * @param array $params
 * @return string|false
 */
function insertAndGetId($pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt ? $pdo->lastInsertId() : false;
}
?>
