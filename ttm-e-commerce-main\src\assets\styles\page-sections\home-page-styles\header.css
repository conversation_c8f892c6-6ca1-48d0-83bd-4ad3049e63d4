.header-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/*!--BELOW---styles for when [data-shadow-content] triggers*/
.low-brightness {
  filter: brightness(25%);
}
/*!--ABOVE---styles for when [data-shadow-content] triggers*/

.header-content__video {
  position: absolute;
  object-fit: cover;
  top: 0;
  height: 100vh; /*?change if needed - later*/
  background: white; /*? setting a bg color to a -video- element seems to fix the "not loading first frame" issue... on iOS devices > iOS-15/*/
}

.header-content__title,
.header-content__subtitle {
  position: relative;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.3rem;
  line-height: 1.2;
  color: var(--style-color-white);
  font-family: var(--font-family-light), "Alexandria";
  padding: 0 1rem;
  pointer-events: none;
}

.header-content__title {
  font-size: 1.7rem;
}

.header-content__subtitle {
  font-size: 0.8rem;
}

.arrow {
  position: relative;
  color: var(--style-color-white);
  font-size: 1.5rem;
  animation: arrowAnimation 1.3s infinite;
}
