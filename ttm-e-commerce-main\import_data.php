<?php
/**
 * ملف إدراج البيانات من JSON إلى قاعدة البيانات
 * يقوم بنقل بيانات الساعات والمستخدمين من ملفات JSON إلى قاعدة البيانات
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدراج البيانات إلى قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007cba;
            background-color: #f8f9fa;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            border-left-color: #17a2b8;
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
            color: #856404;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📥 إدراج البيانات إلى قاعدة البيانات</h1>
            <p>نقل بيانات الساعات والمستخدمين من ملفات JSON إلى قاعدة البيانات</p>
        </div>

        <?php
        $results = [];
        $totalOperations = 0;
        $successfulOperations = 0;

        try {
            // الحصول على الاتصال بقاعدة البيانات
            $pdo = getConnection();
            
            // قراءة ملف بيانات الساعات
            $watchesJsonPath = 'src/json-data/watches-data.json';
            if (file_exists($watchesJsonPath)) {
                $watchesJson = file_get_contents($watchesJsonPath);
                $watchesData = json_decode($watchesJson, true);
                
                if ($watchesData && isset($watchesData['watches_data'])) {
                    $results[] = [
                        'type' => 'info',
                        'title' => 'قراءة ملف بيانات الساعات',
                        'message' => 'تم العثور على ' . count($watchesData['watches_data']) . ' ساعة في ملف JSON'
                    ];
                    
                    // إدراج بيانات الساعات
                    $insertedWatches = 0;
                    $skippedWatches = 0;
                    
                    foreach ($watchesData['watches_data'] as $watch) {
                        $totalOperations++;
                        
                        try {
                            // التحقق من وجود الساعة مسبقاً
                            $checkSql = "SELECT COUNT(*) FROM products WHERE name = ?";
                            $checkStmt = $pdo->prepare($checkSql);
                            $checkStmt->execute([$watch['roller_title']]);
                            
                            if ($checkStmt->fetchColumn() > 0) {
                                $skippedWatches++;
                                continue; // تخطي إذا كانت موجودة
                            }
                            
                            // إدراج الساعة
                            $sql = "INSERT INTO products (
                                name, description, price, image, category, status
                            ) VALUES (?, ?, ?, ?, ?, 'active')";
                            
                            $stmt = $pdo->prepare($sql);
                            $result = $stmt->execute([
                                $watch['roller_title'],
                                $watch['about_text'] ?? $watch['roller_about'],
                                floatval($watch['watch_price']),
                                $watch['roller_src'] ?? 'assets/images/default-watch.jpg',
                                'watches'
                            ]);
                            
                            if ($result) {
                                $insertedWatches++;
                                $successfulOperations++;
                            }
                            
                        } catch (Exception $e) {
                            $results[] = [
                                'type' => 'error',
                                'title' => 'خطأ في إدراج الساعة: ' . $watch['roller_title'],
                                'message' => $e->getMessage()
                            ];
                        }
                    }
                    
                    $results[] = [
                        'type' => 'success',
                        'title' => 'إدراج بيانات الساعات',
                        'message' => "تم إدراج {$insertedWatches} ساعة جديدة، تم تخطي {$skippedWatches} ساعة موجودة مسبقاً"
                    ];
                    
                } else {
                    $results[] = [
                        'type' => 'error',
                        'title' => 'خطأ في قراءة ملف الساعات',
                        'message' => 'تنسيق ملف JSON غير صحيح'
                    ];
                }
            } else {
                $results[] = [
                    'type' => 'warning',
                    'title' => 'ملف بيانات الساعات غير موجود',
                    'message' => 'لم يتم العثور على ملف: ' . $watchesJsonPath
                ];
            }
            
            // قراءة ملف بيانات المستخدمين
            $usersJsonPath = 'src/json-data/login-users.json';
            if (file_exists($usersJsonPath)) {
                $usersJson = file_get_contents($usersJsonPath);
                $usersData = json_decode($usersJson, true);
                
                if ($usersData && isset($usersData['users'])) {
                    $results[] = [
                        'type' => 'info',
                        'title' => 'قراءة ملف بيانات المستخدمين',
                        'message' => 'تم العثور على ' . count($usersData['users']) . ' مستخدم في ملف JSON'
                    ];
                    
                    // إدراج بيانات المستخدمين
                    $insertedUsers = 0;
                    $skippedUsers = 0;
                    
                    foreach ($usersData['users'] as $user) {
                        $totalOperations++;
                        
                        try {
                            // التحقق من وجود المستخدم مسبقاً
                            $checkSql = "SELECT COUNT(*) FROM admins WHERE username = ?";
                            $checkStmt = $pdo->prepare($checkSql);
                            $checkStmt->execute([$user['username']]);
                            
                            if ($checkStmt->fetchColumn() > 0) {
                                $skippedUsers++;
                                continue; // تخطي إذا كان موجود
                            }
                            
                            // تشفير كلمة المرور
                            $hashedPassword = password_hash($user['password'], PASSWORD_DEFAULT);
                            
                            // إدراج المستخدم
                            $sql = "INSERT INTO admins (
                                username, password, email, full_name, role, status
                            ) VALUES (?, ?, ?, ?, 'admin', 'active')";
                            
                            $stmt = $pdo->prepare($sql);
                            $result = $stmt->execute([
                                $user['username'],
                                $hashedPassword,
                                $user['username'] . '@ttm-ecommerce.com',
                                'مدير النظام - ' . $user['username']
                            ]);
                            
                            if ($result) {
                                $insertedUsers++;
                                $successfulOperations++;
                            }
                            
                        } catch (Exception $e) {
                            $results[] = [
                                'type' => 'error',
                                'title' => 'خطأ في إدراج المستخدم: ' . $user['username'],
                                'message' => $e->getMessage()
                            ];
                        }
                    }
                    
                    $results[] = [
                        'type' => 'success',
                        'title' => 'إدراج بيانات المستخدمين',
                        'message' => "تم إدراج {$insertedUsers} مستخدم جديد، تم تخطي {$skippedUsers} مستخدم موجود مسبقاً"
                    ];
                    
                } else {
                    $results[] = [
                        'type' => 'error',
                        'title' => 'خطأ في قراءة ملف المستخدمين',
                        'message' => 'تنسيق ملف JSON غير صحيح'
                    ];
                }
            } else {
                $results[] = [
                    'type' => 'warning',
                    'title' => 'ملف بيانات المستخدمين غير موجود',
                    'message' => 'لم يتم العثور على ملف: ' . $usersJsonPath
                ];
            }
            
        } catch (Exception $e) {
            $results[] = [
                'type' => 'error',
                'title' => 'خطأ عام',
                'message' => $e->getMessage()
            ];
        }
        
        // حساب نسبة النجاح
        $successRate = $totalOperations > 0 ? ($successfulOperations / $totalOperations) * 100 : 0;
        ?>

        <!-- عرض النتائج -->
        <div class="section <?php echo $successRate >= 80 ? 'success' : ($successRate >= 50 ? 'warning' : 'error'); ?>">
            <h2>📊 ملخص العملية</h2>
            <div class="progress">
                <div class="progress-bar" style="width: <?php echo $successRate; ?>%"></div>
            </div>
            <p><strong>إجمالي العمليات:</strong> <?php echo $totalOperations; ?></p>
            <p><strong>العمليات الناجحة:</strong> <?php echo $successfulOperations; ?></p>
            <p><strong>نسبة النجاح:</strong> <?php echo number_format($successRate, 1); ?>%</p>
        </div>

        <!-- عرض تفاصيل النتائج -->
        <?php foreach ($results as $result): ?>
            <div class="section <?php echo $result['type']; ?>">
                <h3><?php echo htmlspecialchars($result['title']); ?></h3>
                <p><?php echo htmlspecialchars($result['message']); ?></p>
            </div>
        <?php endforeach; ?>

        <!-- عرض البيانات الحالية -->
        <?php
        try {
            // عرض إحصائيات قاعدة البيانات
            $productCount = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'")->fetchColumn();
            $adminCount = $pdo->query("SELECT COUNT(*) FROM admins WHERE status = 'active'")->fetchColumn();
            $orderCount = $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn();
        ?>
            <div class="section info">
                <h2>📈 إحصائيات قاعدة البيانات الحالية</h2>
                <table>
                    <tr>
                        <th>النوع</th>
                        <th>العدد</th>
                    </tr>
                    <tr>
                        <td>المنتجات النشطة</td>
                        <td><?php echo $productCount; ?></td>
                    </tr>
                    <tr>
                        <td>المديرين النشطين</td>
                        <td><?php echo $adminCount; ?></td>
                    </tr>
                    <tr>
                        <td>إجمالي الطلبات</td>
                        <td><?php echo $orderCount; ?></td>
                    </tr>
                </table>
            </div>
        <?php
        } catch (Exception $e) {
            echo '<div class="section error"><h3>خطأ في جلب الإحصائيات</h3><p>' . htmlspecialchars($e->getMessage()) . '</p></div>';
        }
        ?>

        <!-- أزرار التحكم -->
        <div class="section info">
            <h2>🔧 أدوات إضافية</h2>
            <a href="test_connection.php" class="btn">اختبار الاتصال</a>
            <a href="index.html" class="btn">العودة للموقع</a>
            <a href="admin.html" class="btn">لوحة الإدارة</a>
        </div>

        <?php if ($successRate < 100): ?>
        <div class="section warning">
            <h2>⚠️ ملاحظات مهمة</h2>
            <ul>
                <li>تأكد من وجود ملفات JSON في المسارات الصحيحة</li>
                <li>تحقق من صلاحيات قاعدة البيانات</li>
                <li>راجع رسائل الخطأ أعلاه لمعرفة السبب</li>
                <li>يمكنك إعادة تشغيل هذا الملف مرة أخرى بأمان</li>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
