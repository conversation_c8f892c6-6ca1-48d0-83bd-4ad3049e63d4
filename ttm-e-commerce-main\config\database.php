<?php
// إعدادات قاعدة البيانات لموقع taxi365.co.za
// استخدم هذه القيم في معالج التثبيت
define('DB_HOST', 'localhost');
define('DB_USER', 'taxicoza_watches_user'); // سيتم إنشاؤه في cPanel
define('DB_PASS', 'b&@lO]1Zf^(NtP_a'); // ضع كلمة المرور الحقيقية هنا
define('DB_NAME', 'taxicoza_watches_store'); // سيتم إنشاؤها في cPanel

// الاتصال بقاعدة البيانات
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// دالة للحصول على الاتصال
function getConnection() {
    global $pdo;
    return $pdo;
}
?>
