-- TTM E-Commerce Database Schema
-- Database for The Time Meister watch e-commerce website

-- Create database
CREATE DATABASE IF NOT EXISTS ttm_ecommerce;
USE ttm_ecommerce;

-- Users table for login system
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'customer') DEFAULT 'customer',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Watches table for product information
CREATE TABLE watches (
    id VARCHAR(50) PRIMARY KEY,
    page_href VARCHAR(255) NOT NULL,
    roller_srcset TEXT,
    roller_src VARCHAR(255),
    img_alt VARCHAR(255),
    roller_title VARCHAR(100) NOT NULL,
    roller_about TEXT,
    video_src VARCHAR(255),
    video_srcset TEXT,
    header_title VARCHAR(100),
    header_subtitle VARCHAR(255),
    about_title TEXT,
    about_text TEXT,
    watch_price DECIMAL(10, 2) NOT NULL,
    img_srcset TEXT,
    img_src VARCHAR(255),
    style_class VARCHAR(100),
    sidepanel_class VARCHAR(100),
    footer_link_title VARCHAR(100),
    doc_title VARCHAR(255),
    stock_quantity INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Categories table for watch categories
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Watch categories relationship table
CREATE TABLE watch_categories (
    watch_id VARCHAR(50),
    category_id INT,
    PRIMARY KEY (watch_id, category_id),
    FOREIGN KEY (watch_id) REFERENCES watches(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Orders table
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    total_amount DECIMAL(10, 2) NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    shipping_address TEXT,
    billing_address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Order items table
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT,
    watch_id VARCHAR(50),
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (watch_id) REFERENCES watches(id) ON DELETE CASCADE
);

-- Shopping cart table
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    watch_id VARCHAR(50),
    quantity INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (watch_id) REFERENCES watches(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_watch (user_id, watch_id)
);

-- Reviews table
CREATE TABLE reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    watch_id VARCHAR(50),
    rating INT CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (watch_id) REFERENCES watches(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_watches_price ON watches(watch_price);
CREATE INDEX idx_watches_active ON watches(is_active);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_reviews_watch ON reviews(watch_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
