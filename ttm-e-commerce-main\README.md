<a name="readme-top"></a>

<div align="center">
    <img src="/src/assets/media/img/logo/ttm-logo-color.png" alt="logo" width="320"  height="auto" />
    <h1><b>The Time Meister | Watch Website</b></h1>
</div>

---

<!-- TABLE OF CONTENTS -->

# 📗 Table of Contents

- [📖 About the Project](#about-project)
  - [🛠 Built With](#built-with)
    - [Tech Stack](#tech-stack)
  - [🚀 Live Demo](#live-demo)
- [💻 Getting Started](#getting-started)
  - [Setup](#setup)
  - [Prerequisites](#prerequisites)
  - [Install](#install)
  - [Usage](#usage)
  - [Run tests](#run-tests)
- [👥 Authors](#authors)
- [🤝 Contributing](#contributing)
- [⭐️ Show your support](#support)

---

<!-- PROJECT DESCRIPTION -->

# 📖 The Time Meister <a name="about-project"></a>

- The primary objective of this project was to translate provided wireframes and styles into an e-commerce website, complete with multiple pages catering to various user roles, including administrators and clients. Remarkably, this was achieved with just three HTML files, showcasing an efficient and streamlined approach to web development.

- ### PARTIAL CLONE OF: [www.rolex.com](https://web.archive.org/web/20221104205955/https://www.rolex.com/en-us) (October 2022 version - from _web.archive_)

---

#### Learning objectives

- Create a Client page side.
- Create an Administrators page side.
- Introduce CRUD.
- Load data on templates and inject them within the main markup.
- Reuse a single HTML file for different web pages (each watch page).
- Store data across different web pages and reuse it.
- Get familiar with JSON.
- Keep up with Markup and Styles.
- Continue practising JavaScript.

## 🛠 Built With <a name="built-with"></a>

### Tech Stack <a name="tech-stack"></a>

  <ul>
    <li>
      <img src="https://skillicons.dev/icons?i=nodejs"/>
      <a href="https://nodejs.org/en">Node.js</a>
    </li>
    <li>
      <img src="https://skillicons.dev/icons?i=js"/>
      <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">JavaScript</a>
    </li>
    <li>
      <img src="https://skillicons.dev/icons?i=css"/>
      <a href="[https://sass-lang.com/](https://developer.mozilla.org/en-US/docs/Web/CSS)">CSS</a>
    </li>
    <li>
      <img src="https://skillicons.dev/icons?i=html"/>
      <a href="https://developer.mozilla.org/en-US/docs/Web/HTML">HTML</a>
    </li>
  </ul>

### Libraries used:

- #### [UUID](https://cdnjs.com/libraries/uuid/8.3.2)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

---

<!-- LIVE DEMO -->

## 🚀 Live Demo <a name="live-demo"></a>

- [TTM - Live Demo Link](https://iturres.github.io/ttm-e-commerce/)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

---

<!-- GETTING STARTED -->

## 💻 Getting Started <a name="getting-started"></a>

To get a local copy up and running, follow these steps.

### Prerequisites

In order to run this project you need:

### Setup

Clone this repository to your desired folder:

Example commands:

```bash
  cd my-folder
  <NAME_EMAIL>:ITurres/ttm-e-commerce.git
```

### _⚠️Website will load static-js-data if JSON-server is not loaded.👍_

#### _In order for you to see actual CRUD data and be able to log in as **`admin - 123456`** follow the steps below._

#### Within the project folder

```bash
cd src\json-data

```

#### To run JSON login-users data

```bash
json-server -w .\login-users.json

```

#### To run JSON watches data

```bash
json-server -w .\watches-data.json

```

### Install

Install this project's dependencies with:

- N/A

### Usage

To run the project, execute the following command:

- N/A

### Run tests

- N/A

<p align="right">(<a href="#readme-top">back to top</a>)</p>

---

<!-- AUTHORS -->

## 👥 Authors <a name="authors"></a>

👤 **Author1**

- GitHub: [@ITurres](https://github.com/ITurres)
- LinkedIn: [Arthur Emanuel G. Iturres](https://www.linkedin.com/in/arturoemanuelguerraiturres/)
- Angellist / Wellfound: [Arturo (Arthur) Emanuel Guerra Iturres](https://wellfound.com/u/arturo-arthur-emanuel-guerra-iturres)
- Youtube: [Arturo Emanuel Guerra Iturres - Youtube Channel](https://www.youtube.com/channel/UC6GFUFHOtBS9mOuI8EJ6q4g)

<p align="right">(<a href="#readme-top">back to top</a>)</p>

---

<!-- CONTRIBUTING -->

## 🤝 Contributing <a name="contributing"></a>

Contributions, issues, and feature requests are welcome!

Feel free to check the [issues page](https://github.com/ITurres/ttm-e-commerce/issues).

<p align="right">(<a href="#readme-top">back to top</a>)</p>

---

<!-- SUPPORT -->

## ⭐️ Show your support <a name="support"></a>

Give a ⭐ if you liked this project!

<p align="right">(<a href="#readme-top">back to top</a>)</p>

---

<!-- LICENSE -->

## 📝 License <a name="license"></a>

This project is [MIT](./LICENSE) licensed.

<p align="right">(<a href="#readme-top">back to top</a>)</p>

---
