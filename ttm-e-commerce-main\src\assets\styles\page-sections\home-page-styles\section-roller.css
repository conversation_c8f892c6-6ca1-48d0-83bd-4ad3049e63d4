/*!--BELOW---styles for when [data-shadow-content] triggers*/
.low-brightness {
  filter: brightness(25%);
}
/*!--ABOVE---styles for when [data-shadow-content] triggers*/

section.watches {
  background-color: var(--style-color-white);
}

.watches__roller-container {
  margin-left: 5%;
}

.roller {
  overflow: auto;
  white-space: nowrap;
  padding-bottom: 2rem;
  margin-bottom: 4rem;
}

div.roller::-webkit-scrollbar {
  height: 0.3rem;
}

div.roller::-webkit-scrollbar-track {
  background: var(--style-milgauss-gray);
}

div.roller::-webkit-scrollbar-thumb {
  background: var(--style-color-green);
  border-radius: 1rem;
}

.roller__item {
  display: inline-block;
  margin: 0 0.1rem;
  text-decoration: none;
  width: fit-content;
  overflow: hidden;
  border-top-left-radius: 1.3rem;
  border-top-right-radius: 1.3rem;
  height: 100%;
  width: 80%;
}

.roller__img {
  border-radius: 1.3rem;
  transform-origin: center;
  transition: all 0.5s ease;
  width: 100%;
}

.roller__item:hover .roller__img {
  transform: scale(1.1);
}

.item__text--holder {
  position: relative;
  z-index: 10;
  background-color: var(--style-color-white);
}

.item__title {
  font-size: 0.9rem;
  color: var(--style-color-offBlack);
  text-transform: uppercase;
  font-family: var(--font-family-bold), "Alexandria";
  font-weight: 600;
  letter-spacing: 0.1rem;
  padding-top: 0.4rem;
}

.item__about {
  font-size: 0.8rem;
  color: var(--style-color-offBlack);
}
