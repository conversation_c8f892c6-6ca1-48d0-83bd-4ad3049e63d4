<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

// التحقق من طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

try {
    // الحصول على الاتصال بقاعدة البيانات
    $pdo = getConnection();
    
    switch ($method) {
        case 'GET':
            handleGetProducts($pdo);
            break;
            
        case 'POST':
            handleAddProduct($pdo);
            break;
            
        case 'PUT':
            handleUpdateProduct($pdo);
            break;
            
        case 'DELETE':
            handleDeleteProduct($pdo);
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'طريقة الطلب غير مسموحة'
            ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * جلب جميع المنتجات
 */
function handleGetProducts($pdo) {
    try {
        $sql = "SELECT * FROM products WHERE status != 'deleted' ORDER BY created_at DESC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $products = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $products
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في جلب المنتجات: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * إضافة منتج جديد
 */
function handleAddProduct($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($input['name']) || !isset($input['price'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'اسم المنتج والسعر مطلوبان'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        $sql = "INSERT INTO products (name, description, price, image, category) 
                VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            $input['price'],
            $input['image'] ?? 'assets/images/default-watch.jpg',
            $input['category'] ?? 'watches'
        ]);
        
        if ($result) {
            $productId = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة المنتج بنجاح',
                'product_id' => $productId
            ], JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception('فشل في إضافة المنتج');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في إضافة المنتج: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * تحديث منتج موجود
 */
function handleUpdateProduct($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف المنتج مطلوب'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        // التحقق من وجود المنتج
        $checkSql = "SELECT id FROM products WHERE id = ? AND status != 'deleted'";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([$input['id']]);
        
        if (!$checkStmt->fetch()) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'المنتج غير موجود'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        
        // تحديث المنتج
        $sql = "UPDATE products SET 
                name = COALESCE(?, name),
                description = COALESCE(?, description),
                price = COALESCE(?, price),
                image = COALESCE(?, image),
                category = COALESCE(?, category),
                status = COALESCE(?, status),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $input['name'] ?? null,
            $input['description'] ?? null,
            $input['price'] ?? null,
            $input['image'] ?? null,
            $input['category'] ?? null,
            $input['status'] ?? null,
            $input['id']
        ]);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المنتج بنجاح'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception('فشل في تحديث المنتج');
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في تحديث المنتج: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * حذف منتج
 */
function handleDeleteProduct($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    // التحقق من البيانات المطلوبة
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'معرف المنتج مطلوب'
        ], JSON_UNESCAPED_UNICODE);
        return;
    }
    
    try {
        // حذف منطقي (تغيير الحالة إلى deleted)
        $sql = "UPDATE products SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([$input['id']]);
        
        if ($result && $stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف المنتج بنجاح'
            ], JSON_UNESCAPED_UNICODE);
        } else {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'المنتج غير موجود'
            ], JSON_UNESCAPED_UNICODE);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في حذف المنتج: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}
?>
