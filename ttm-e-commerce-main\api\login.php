<?php
session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مسموحة'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// الحصول على البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

// التحقق من وجود البيانات المطلوبة
if (!isset($input['username']) || !isset($input['password'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'اسم المستخدم وكلمة المرور مطلوبان'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$username = trim($input['username']);
$password = trim($input['password']);

// التحقق من عدم كون البيانات فارغة
if (empty($username) || empty($password)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'اسم المستخدم وكلمة المرور لا يمكن أن يكونا فارغين'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // الحصول على الاتصال بقاعدة البيانات
    $pdo = getConnection();
    
    // البحث عن المستخدم في قاعدة البيانات
    $sql = "SELECT id, username, password, email, full_name, role, status 
            FROM admins 
            WHERE username = ? AND status = 'active'";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if ($user) {
        // التحقق من كلمة المرور
        if (password_verify($password, $user['password'])) {
            // تحديث وقت آخر تسجيل دخول
            $updateSql = "UPDATE admins SET last_login = NOW() WHERE id = ?";
            $updateStmt = $pdo->prepare($updateSql);
            $updateStmt->execute([$user['id']]);
            
            // حفظ بيانات المستخدم في الجلسة
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['logged_in'] = true;
            
            // إرجاع استجابة نجح
            echo json_encode([
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'full_name' => $user['full_name'],
                    'role' => $user['role']
                ]
            ], JSON_UNESCAPED_UNICODE);
        } else {
            // كلمة المرور خاطئة
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
            ], JSON_UNESCAPED_UNICODE);
        }
    } else {
        // المستخدم غير موجود
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (PDOException $e) {
    // في حالة حدوث خطأ في قاعدة البيانات
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    // في حالة حدوث خطأ عام
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم',
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
