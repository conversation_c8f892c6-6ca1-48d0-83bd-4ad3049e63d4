-- قاعدة البيانات موجودة بالفعل، لا نحتاج لإنشائها
-- CREATE DATABASE IF NOT EXISTS watches_store CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE watches_store;

-- جدول المنتجات
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image VARCHAR(500) DEFAULT 'assets/images/default-watch.jpg',
    category VARCHAR(100) DEFAULT 'watches',
    status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدو<PERSON> الطلبات
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_address TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول عناصر الطلبات
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    subtotal DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- جدول المديرين
CREATE TABLE admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager') DEFAULT 'admin',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج مدير افتراضي
INSERT INTO admins (username, password, email, full_name, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'مدير النظام', 'admin');
-- كلمة المرور الافتراضية: password

-- إدراج منتجات تجريبية
INSERT INTO products (name, description, price, image) VALUES
('ساعة رولكس كلاسيكية', 'ساعة رولكس فاخرة بتصميم كلاسيكي أنيق، مصنوعة من الذهب الخالص مع حركة سويسرية دقيقة', 25000.00, 'https://images.unsplash.com/photo-1523170335258-f5c6c6bd6eaf?w=400&h=400&fit=crop'),
('ساعة أوميغا سبورت', 'ساعة أوميغا رياضية مقاومة للماء حتى 300 متر، مثالية للأنشطة الرياضية والغوص', 18000.00, 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=400&fit=crop'),
('ساعة كارتييه للنساء', 'ساعة كارتييه أنيقة للنساء مرصعة بالألماس، تصميم فرنسي راقي وحركة كوارتز دقيقة', 35000.00, 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=400&h=400&fit=crop'),
('ساعة تاغ هوير فورمولا', 'ساعة تاغ هوير مستوحاة من عالم سباق الفورمولا ون، كرونوغراف دقيق وتصميم رياضي', 22000.00, 'https://images.unsplash.com/photo-1434056886845-dac89ffe9b56?w=400&h=400&fit=crop'),
('ساعة باتيك فيليب كلاسيك', 'ساعة باتيك فيليب الفاخرة بحركة يدوية، تحفة فنية سويسرية بتصميم خالد', 85000.00, 'https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=400&h=400&fit=crop'),
('ساعة أبل الذكية', 'ساعة أبل الذكية الجيل الأحدث مع مراقب صحي متقدم وإمكانيات اتصال لاسلكي', 8500.00, 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=400&fit=crop'),
('ساعة سيكو أوتوماتيك', 'ساعة سيكو يابانية بحركة أوتوماتيكية، تصميم كلاسيكي بسعر مناسب وجودة عالية', 3500.00, 'https://images.unsplash.com/photo-1533139502658-0198f920d8e8?w=400&h=400&fit=crop'),
('ساعة كاسيو جي شوك', 'ساعة كاسيو جي شوك مقاومة للصدمات والماء، مثالية للاستخدام اليومي والأنشطة الخارجية', 1200.00, 'https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?w=400&h=400&fit=crop');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);
CREATE INDEX idx_admins_username ON admins(username);
CREATE INDEX idx_admins_status ON admins(status);

-- إنشاء views للتقارير
CREATE VIEW sales_summary AS
SELECT 
    DATE(o.created_at) as sale_date,
    COUNT(o.id) as total_orders,
    SUM(o.total_amount) as total_sales,
    AVG(o.total_amount) as average_order_value
FROM orders o 
WHERE o.status IN ('completed', 'delivered')
GROUP BY DATE(o.created_at)
ORDER BY sale_date DESC;

CREATE VIEW product_sales AS
SELECT 
    p.id,
    p.name,
    p.price,
    COALESCE(SUM(oi.quantity), 0) as total_sold,
    COALESCE(SUM(oi.subtotal), 0) as total_revenue
FROM products p
LEFT JOIN order_items oi ON p.id = oi.product_id
LEFT JOIN orders o ON oi.order_id = o.id AND o.status IN ('completed', 'delivered')
WHERE p.status = 'active'
GROUP BY p.id, p.name, p.price
ORDER BY total_sold DESC;

CREATE VIEW monthly_sales AS
SELECT 
    YEAR(o.created_at) as year,
    MONTH(o.created_at) as month,
    MONTHNAME(o.created_at) as month_name,
    COUNT(o.id) as total_orders,
    SUM(o.total_amount) as total_sales
FROM orders o 
WHERE o.status IN ('completed', 'delivered')
GROUP BY YEAR(o.created_at), MONTH(o.created_at)
ORDER BY year DESC, month DESC;
