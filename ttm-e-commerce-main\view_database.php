<?php
/**
 * عرض محتويات قاعدة البيانات
 * يعرض جميع البيانات الموجودة في الجداول المختلفة
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض محتويات قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            background-color: #f8f9fa;
            border-left: 4px solid #007cba;
        }
        .section h2 {
            margin-top: 0;
            color: #007cba;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007cba;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .status-active { background-color: #28a745; }
        .status-inactive { background-color: #6c757d; }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-processing { background-color: #17a2b8; }
        .status-delivered { background-color: #28a745; }
        .status-cancelled { background-color: #dc3545; }
        .price {
            font-weight: bold;
            color: #28a745;
        }
        .btn {
            background-color: #007cba;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 2px;
            font-size: 12px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-sm {
            padding: 4px 8px;
            font-size: 11px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .image-preview {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 4px;
        }
        .text-truncate {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 عرض محتويات قاعدة البيانات</h1>
            <p>TTM E-Commerce Database Contents</p>
        </div>

        <?php
        try {
            // الحصول على الاتصال بقاعدة البيانات
            $pdo = getConnection();
            
            // جلب الإحصائيات العامة
            $stats = [
                'products' => $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'")->fetchColumn(),
                'total_products' => $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn(),
                'orders' => $pdo->query("SELECT COUNT(*) FROM orders")->fetchColumn(),
                'admins' => $pdo->query("SELECT COUNT(*) FROM admins WHERE status = 'active'")->fetchColumn(),
                'total_sales' => $pdo->query("SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE status IN ('delivered', 'processing')")->fetchColumn()
            ];
        ?>

        <!-- الإحصائيات العامة -->
        <div class="section">
            <h2>📈 الإحصائيات العامة</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['products']; ?></div>
                    <div class="stat-label">المنتجات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['orders']; ?></div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['admins']; ?></div>
                    <div class="stat-label">المديرين النشطين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_sales'], 0); ?></div>
                    <div class="stat-label">إجمالي المبيعات (جنيه)</div>
                </div>
            </div>
        </div>

        <!-- عرض المنتجات -->
        <div class="section">
            <h2>🛍️ المنتجات</h2>
            <?php
            $productsSql = "SELECT * FROM products ORDER BY created_at DESC LIMIT 10";
            $productsStmt = $pdo->prepare($productsSql);
            $productsStmt->execute();
            $products = $productsStmt->fetchAll();
            
            if (!empty($products)):
            ?>
            <table>
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>الوصف</th>
                        <th>السعر</th>
                        <th>الفئة</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($products as $product): ?>
                    <tr>
                        <td>
                            <img src="<?php echo htmlspecialchars($product['image']); ?>" 
                                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                 class="image-preview"
                                 onerror="this.src='https://via.placeholder.com/50x50?text=No+Image'">
                        </td>
                        <td><?php echo htmlspecialchars($product['name']); ?></td>
                        <td>
                            <div class="text-truncate" title="<?php echo htmlspecialchars($product['description']); ?>">
                                <?php echo htmlspecialchars($product['description']); ?>
                            </div>
                        </td>
                        <td class="price"><?php echo number_format($product['price'], 0); ?> جنيه</td>
                        <td><?php echo htmlspecialchars($product['category']); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $product['status']; ?>">
                                <?php echo $product['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </td>
                        <td><?php echo date('Y-m-d H:i', strtotime($product['created_at'])); ?></td>
                        <td>
                            <a href="#" class="btn btn-sm">تعديل</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php if ($stats['total_products'] > 10): ?>
                <p><strong>عرض 10 من أصل <?php echo $stats['total_products']; ?> منتج</strong></p>
            <?php endif; ?>
            <?php else: ?>
                <p>لا توجد منتجات في قاعدة البيانات</p>
            <?php endif; ?>
        </div>

        <!-- عرض الطلبات -->
        <div class="section">
            <h2>📦 الطلبات</h2>
            <?php
            $ordersSql = "SELECT * FROM orders ORDER BY created_at DESC LIMIT 10";
            $ordersStmt = $pdo->prepare($ordersSql);
            $ordersStmt->execute();
            $orders = $ordersStmt->fetchAll();
            
            if (!empty($orders)):
            ?>
            <table>
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>اسم العميل</th>
                        <th>الهاتف</th>
                        <th>العنوان</th>
                        <th>المبلغ الإجمالي</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                    <tr>
                        <td>#<?php echo $order['id']; ?></td>
                        <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                        <td><?php echo htmlspecialchars($order['customer_phone']); ?></td>
                        <td>
                            <div class="text-truncate" title="<?php echo htmlspecialchars($order['customer_address']); ?>">
                                <?php echo htmlspecialchars($order['customer_address']); ?>
                            </div>
                        </td>
                        <td class="price"><?php echo number_format($order['total_amount'], 0); ?> جنيه</td>
                        <td>
                            <span class="status-badge status-<?php echo $order['status']; ?>">
                                <?php 
                                $statusLabels = [
                                    'pending' => 'في الانتظار',
                                    'confirmed' => 'مؤكد',
                                    'processing' => 'قيد التحضير',
                                    'shipped' => 'تم الشحن',
                                    'delivered' => 'تم التسليم',
                                    'cancelled' => 'ملغي'
                                ];
                                echo $statusLabels[$order['status']] ?? $order['status'];
                                ?>
                            </span>
                        </td>
                        <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                        <td>
                            <a href="#" class="btn btn-sm">عرض</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
                <p>لا توجد طلبات في قاعدة البيانات</p>
            <?php endif; ?>
        </div>

        <!-- عرض المديرين -->
        <div class="section">
            <h2>👥 المديرين</h2>
            <?php
            $adminsSql = "SELECT id, username, email, full_name, role, status, last_login, created_at FROM admins ORDER BY created_at DESC";
            $adminsStmt = $pdo->prepare($adminsSql);
            $adminsStmt->execute();
            $admins = $adminsStmt->fetchAll();
            
            if (!empty($admins)):
            ?>
            <table>
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>اسم المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الاسم الكامل</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>آخر تسجيل دخول</th>
                        <th>تاريخ الإنشاء</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($admins as $admin): ?>
                    <tr>
                        <td><?php echo $admin['id']; ?></td>
                        <td><?php echo htmlspecialchars($admin['username']); ?></td>
                        <td><?php echo htmlspecialchars($admin['email']); ?></td>
                        <td><?php echo htmlspecialchars($admin['full_name']); ?></td>
                        <td><?php echo $admin['role'] === 'admin' ? 'مدير' : 'مدير فرعي'; ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $admin['status']; ?>">
                                <?php echo $admin['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </td>
                        <td>
                            <?php echo $admin['last_login'] ? date('Y-m-d H:i', strtotime($admin['last_login'])) : 'لم يسجل دخول'; ?>
                        </td>
                        <td><?php echo date('Y-m-d H:i', strtotime($admin['created_at'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
                <p>لا يوجد مديرين في قاعدة البيانات</p>
            <?php endif; ?>
        </div>

        <?php
        } catch (Exception $e) {
            echo '<div class="section" style="border-left-color: #dc3545; background-color: #f8d7da; color: #721c24;">';
            echo '<h2>❌ خطأ في الاتصال بقاعدة البيانات</h2>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
        }
        ?>

        <!-- أزرار التحكم -->
        <div class="section">
            <h2>🔧 أدوات إضافية</h2>
            <a href="import_data.php" class="btn">إدراج البيانات الأساسية</a>
            <a href="add_sample_data.php" class="btn">إضافة بيانات تجريبية</a>
            <a href="test_connection.php" class="btn">اختبار الاتصال</a>
            <a href="index.html" class="btn">العودة للموقع</a>
            <a href="admin.html" class="btn">لوحة الإدارة</a>
        </div>
    </div>
</body>
</html>
